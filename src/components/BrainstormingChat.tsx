import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Lightbulb,
  FileText,
  Target,
  Users,
  Sparkles,
  Bot,
  User,
  Loader2,
  Copy,
  Download,
  RefreshCw,
  Zap,
  Brain,
  Rocket,
  Star,
  MessageSquare,
  Plus,
  X
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { brainstormApi, type BrainstormMessage as ApiBrainstormMessage, type BrainstormStreamEvent, type BrainstormCompleteEvent, type BrainstormErrorEvent } from "@/lib/brainstorm-api";
import { useToastContext } from "@/contexts/ToastContext";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { useBrainstormStore } from "@/stores/brainstormStore";
import { universalPersistence } from "@/lib/universal-persistence";
import type { BrainstormSessionInfo } from "@/types/unified-session";

interface BrainstormingChatProps {
  onClose?: () => void;
  onBack?: () => void;
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  status?: 'pending' | 'success' | 'error';
  suggestions?: string[];
  choices?: BrainstormingChoice[];
}

interface BrainstormingChoice {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: React.ComponentType<any>;
  category: 'idea' | 'prd' | 'analysis' | 'strategy';
}

const BRAINSTORMING_CHOICES: BrainstormingChoice[] = [
  {
    id: 'idea-generation',
    title: 'Generate Ideas',
    description: 'Brainstorm creative solutions and innovative concepts',
    prompt: 'Help me brainstorm creative ideas for: ',
    icon: Lightbulb,
    category: 'idea'
  },
  {
    id: 'prd-creation',
    title: 'Create PRD',
    description: 'Generate a comprehensive Product Requirements Document',
    prompt: 'Create a detailed PRD for: ',
    icon: FileText,
    category: 'prd'
  },
  {
    id: 'feature-analysis',
    title: 'Feature Analysis',
    description: 'Analyze features, pros/cons, and implementation approaches',
    prompt: 'Analyze the features and implementation for: ',
    icon: Target,
    category: 'analysis'
  },
  {
    id: 'user-personas',
    title: 'User Personas',
    description: 'Define target users and their needs',
    prompt: 'Help me define user personas and their needs for: ',
    icon: Users,
    category: 'strategy'
  },
  {
    id: 'competitive-analysis',
    title: 'Competitive Analysis',
    description: 'Research competitors and market positioning',
    prompt: 'Conduct a competitive analysis for: ',
    icon: Brain,
    category: 'analysis'
  },
  {
    id: 'mvp-planning',
    title: 'MVP Planning',
    description: 'Plan minimum viable product features and roadmap',
    prompt: 'Help me plan an MVP for: ',
    icon: Rocket,
    category: 'strategy'
  },
  {
    id: 'problem-solving',
    title: 'Problem Solving',
    description: 'Break down complex problems into actionable solutions',
    prompt: 'Help me solve this problem: ',
    icon: Zap,
    category: 'idea'
  },
  {
    id: 'innovation-workshop',
    title: 'Innovation Workshop',
    description: 'Facilitate creative thinking and innovation sessions',
    prompt: 'Run an innovation workshop for: ',
    icon: Star,
    category: 'idea'
  }
];

const QUICK_STARTERS = [
  "A mobile app for productivity",
  "An AI-powered tool for developers",
  "A platform for remote collaboration",
  "A solution for sustainable living",
  "An educational technology product",
  "A health and wellness application"
];

const MODELS = [
  { value: "sonnet", label: "Claude 4 Sonnet" },
  { value: "opus", label: "Claude 4 Opus" }
];

export const BrainstormingChat: React.FC<BrainstormingChatProps> = ({
  onClose,
  className
}) => {
  // Use persistent session management
  const {
    currentSessionId,
    createSession,
    setCurrentSession,
    addMessage,
    updateMessage,
    getCurrentSession
  } = useBrainstormStore();
  
  const [sessionId, setSessionId] = useState<string>(
    currentSessionId || `brainstorm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  );
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedModel, setSelectedModel] = useState("sonnet");
  const [showChoices, setShowChoices] = useState(true);
  const [streamingContent, setStreamingContent] = useState<string>("");
  const [currentAssistantMessageId, setCurrentAssistantMessageId] = useState<string | null>(null);
  
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const isListeningRef = useRef(false);
  const isMountedRef = useRef(true);
  const { error: showError, info: showInfo, success: showSuccess } = useToastContext();

  // Initialize or restore session on mount
  useEffect(() => {
    const initializeSession = async () => {
      try {
        let activeSessionId = currentSessionId;
        
        if (!activeSessionId) {
          // Create new session if none exists
          activeSessionId = createSession("Brainstorming Session");
          setCurrentSession(activeSessionId);
        }
        
        setSessionId(activeSessionId);
        
        // Load existing messages from session
        const currentSession = getCurrentSession();
        if (currentSession && currentSession.messages.length > 0) {
          // Convert store messages to component format
          const convertedMessages: ChatMessage[] = currentSession.messages.map(msg => ({
            id: msg.id,
            type: msg.type,
            content: msg.content,
            timestamp: msg.timestamp,
            status: msg.status,
            suggestions: msg.suggestions,
            extractedIdeas: msg.extractedIdeas
          }));
          setMessages(convertedMessages);
          setShowChoices(false);
        } else {
          // Set welcome message for new session
          const welcomeMessage: ChatMessage = {
            id: 'welcome',
            type: 'system',
            content: "🚀 Welcome to your AI Brainstorming Assistant! I'm here to help you generate ideas, create PRDs, and turn your concepts into actionable plans. Choose a brainstorming type below or describe what you'd like to explore.",
            timestamp: new Date().toISOString()
          };
          setMessages([welcomeMessage]);
          
          // Save welcome message to store
          addMessage(activeSessionId, {
            type: 'system',
            content: welcomeMessage.content
          });
        }
        
        // Save session to universal persistence
        const sessionInfo: BrainstormSessionInfo = {
          id: activeSessionId,
          type: 'brainstorm',
          title: currentSession?.title || "Brainstorming Session",
          createdAt: currentSession?.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          status: 'active',
          childSessionIds: [],
          metadata: currentSession?.metadata || {
            totalIdeas: 0,
            totalClusters: 0,
            lastActivity: new Date().toISOString(),
            personas: [],
            exportHistory: []
          },
          sessionData: currentSession || {
            id: activeSessionId,
            title: "Brainstorming Session",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            messages: [],
            ideas: [],
            tags: [],
            metadata: {
              totalIdeas: 0,
              totalClusters: 0,
              lastActivity: new Date().toISOString(),
              personas: [],
              exportHistory: []
            }
          }
        };
        
        await universalPersistence.saveSession(sessionInfo);
      } catch (error) {
        console.error('Failed to initialize brainstorming session:', error);
        showError('Failed to initialize session');
      }
    };
    
    initializeSession();
  }, [createSession, setCurrentSession, getCurrentSession, addMessage, currentSessionId, showError]);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages, streamingContent]);

  // Cleanup event listeners on unmount
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      console.log('[BrainstormingChat] Component unmounting, cleaning up listeners');
      isMountedRef.current = false;
      isListeningRef.current = false;
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
    };
  }, []);

  const handleChoiceSelect = (choice: BrainstormingChoice) => {
    setInput(choice.prompt);
    setShowChoices(false);
    inputRef.current?.focus();
  };

  const handleQuickStarter = (starter: string) => {
    setInput(prev => prev + starter);
    inputRef.current?.focus();
  };

  const handleStreamMessage = (event: any) => {
    if (!isMountedRef.current) return;
    
    const data = event.payload as BrainstormStreamEvent;
    
    if (data.content) {
      setStreamingContent(data.accumulated || data.content);
    }
  };

  const setupEventListeners = () => {
    if (isListeningRef.current || !isMountedRef.current) return;
    
    console.log('[BrainstormingChat] Setting up event listeners');
    isListeningRef.current = true;

    // Listen to chat-specific events
    listen('chat-stream', handleStreamMessage).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });

    listen('chat-error', (event: any) => {
      if (!isMountedRef.current) return;
      const data = event.payload as BrainstormErrorEvent;
      console.error('[BrainstormingChat] Chat error:', data.error);
      setError(data.error || 'An error occurred');
      setIsProcessing(false);
      
      // Remove the placeholder message on error
      if (currentAssistantMessageId) {
        setMessages(prev => prev.filter(msg => msg.id !== currentAssistantMessageId));
        setCurrentAssistantMessageId(null);
      }
    }).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });

    listen('chat-complete', (event: any) => {
      if (!isMountedRef.current) return;
      const data = event.payload as BrainstormCompleteEvent;
      console.log('[BrainstormingChat] Chat complete');
      
      // Finalize the streaming content into a message
      if (currentAssistantMessageId && sessionId) {
        const finalContent = data.content || streamingContent;
        const suggestions = generateFollowUpSuggestions();
        
        // Update local state
        setMessages(prev => prev.map(msg =>
          msg.id === currentAssistantMessageId
            ? { ...msg, content: finalContent, status: 'success', suggestions }
            : msg
        ));
        
        // Update message in store
        updateMessage(sessionId, currentAssistantMessageId, {
          content: finalContent,
          status: 'success',
          suggestions
        });
        
        // Save updated session to universal persistence
        const currentSession = getCurrentSession();
        if (currentSession) {
          const sessionInfo: BrainstormSessionInfo = {
            id: sessionId,
            type: 'brainstorm',
            title: currentSession.title,
            createdAt: currentSession.createdAt,
            updatedAt: new Date().toISOString(),
            status: 'active',
            childSessionIds: [],
            metadata: currentSession.metadata,
            sessionData: currentSession
          };
          
          universalPersistence.saveSession(sessionInfo).catch(error => {
            console.error('Failed to persist session after message completion:', error);
          });
        }
        
        setStreamingContent("");
        setCurrentAssistantMessageId(null);
      }
      
      setIsProcessing(false);
    }).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });
  };

  const handleSubmit = async () => {
    if (!input.trim() || isProcessing || !sessionId) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: input,
      timestamp: new Date().toISOString()
    };

    // Add user message to local state and store
    setMessages(prev => [...prev, userMessage]);
    addMessage(sessionId, {
      type: 'user',
      content: input
    });

    const currentInput = input;
    setInput("");
    setIsProcessing(true);
    setShowChoices(false);
    setStreamingContent("");

    // Create a placeholder assistant message for streaming
    const assistantMessageId = `res_${Date.now()}`;
    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      type: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      status: 'pending'
    };
    setMessages(prev => [...prev, assistantMessage]);
    setCurrentAssistantMessageId(assistantMessageId);

    // Add placeholder assistant message to store
    addMessage(sessionId, {
      type: 'assistant',
      content: '',
      status: 'pending'
    });

    try {
      // Set up event listeners if not already set up
      setupEventListeners();

      // Build conversation history
      const apiMessages: ApiBrainstormMessage[] = [];

      // Add previous conversation messages (excluding system messages)
      messages
        .filter(msg => msg.type !== 'system')
        .forEach(msg => {
          apiMessages.push({
            role: msg.type as 'user' | 'assistant',
            content: msg.content
          });
        });

      // Add current user message
      apiMessages.push({
        role: 'user',
        content: currentInput
      });

      // Send brainstorm chat request
      await brainstormApi.chat({
        sessionId,
        messages: apiMessages,
        model: selectedModel,
        temperature: 0.7,
        maxTokens: 4096
      });

    } catch (error) {
      console.error('Error processing brainstorming request:', error);
      
      // Remove the placeholder message from both local state and store
      setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
      
      let errorContent = error instanceof Error ? error.message : 'Failed to process request';
      
      // Add helpful message for Claude binary errors
      if (errorContent.includes('Claude binary')) {
        errorContent += '\n\nMake sure Claude Code CLI is properly installed and configured.';
      }
      
      const errorMessage: ChatMessage = {
        id: `err_${Date.now()}`,
        type: 'system',
        content: errorContent,
        timestamp: new Date().toISOString(),
        status: 'error'
      };
      setMessages(prev => [...prev, errorMessage]);
      
      // Add error message to store
      addMessage(sessionId, {
        type: 'system',
        content: errorContent,
        status: 'error'
      });
      
      setIsProcessing(false);
      setStreamingContent("");
      setCurrentAssistantMessageId(null);
      showError("Failed to process brainstorming request");
    }
  };

  const handleCancelExecution = async () => {
    if (!isProcessing) return;
    
    try {
      // Cancel the brainstorm session
      await brainstormApi.cancel(sessionId);
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
      isListeningRef.current = false;
      
      // Reset states
      setIsProcessing(false);
      setError(null);
      setStreamingContent("");
      
      // Update the current assistant message to show it was cancelled
      if (currentAssistantMessageId) {
        setMessages(prev => prev.map(msg => 
          msg.id === currentAssistantMessageId 
            ? { ...msg, content: streamingContent || "*Session cancelled*", status: 'error' }
            : msg
        ));
        setCurrentAssistantMessageId(null);
      }
      
      showInfo("Session cancelled");
    } catch (err) {
      console.error("Failed to cancel brainstorm:", err);
      showError("Failed to cancel session");
    }
  };

  const generateFollowUpSuggestions = (): string[] => {
    // Generate contextual suggestions based on the response content
    const suggestions = [
      "Create a detailed user journey map",
      "Develop a competitive analysis",
      "Design a technical architecture",
      "Plan a go-to-market strategy",
      "Create wireframes and mockups",
      "Explore implementation details",
      "Analyze potential risks",
      "Define success metrics"
    ];
    
    // Return a random subset of suggestions
    return suggestions.sort(() => 0.5 - Math.random()).slice(0, 5);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
    inputRef.current?.focus();
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    showSuccess("Content copied to clipboard");
  };

  const exportChat = () => {
    const chatContent = messages
      .filter(m => m.type !== 'system' || m.id === 'welcome')
      .map(m => `**${m.type.toUpperCase()}** (${new Date(m.timestamp).toLocaleString()})\n${m.content}\n`)
      .join('\n---\n\n');
    
    const blob = new Blob([chatContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `brainstorming-session-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetChat = async () => {
    // Clean up any active session
    if (isProcessing) {
      await handleCancelExecution();
    }
    
    // Clean up listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    isListeningRef.current = false;
    
    // Create a new session
    const newSessionId = createSession("New Brainstorming Session");
    setCurrentSession(newSessionId);
    setSessionId(newSessionId);
    
    // Set welcome message
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      type: 'system',
      content: "🚀 Welcome to your AI Brainstorming Assistant! I'm here to help you generate ideas, create PRDs, and turn your concepts into actionable plans. Choose a brainstorming type below or describe what you'd like to explore.",
      timestamp: new Date().toISOString()
    };
    
    setMessages([welcomeMessage]);
    
    // Save welcome message to store
    addMessage(newSessionId, {
      type: 'system',
      content: welcomeMessage.content
    });
    
    // Save new session to universal persistence
    const currentSession = getCurrentSession();
    if (currentSession) {
      const sessionInfo: BrainstormSessionInfo = {
        id: newSessionId,
        type: 'brainstorm',
        title: currentSession.title,
        createdAt: currentSession.createdAt,
        updatedAt: new Date().toISOString(),
        status: 'active',
        childSessionIds: [],
        metadata: currentSession.metadata,
        sessionData: currentSession
      };
      
      try {
        await universalPersistence.saveSession(sessionInfo);
      } catch (error) {
        console.error('Failed to persist new session:', error);
      }
    }
    
    setShowChoices(true);
    setStreamingContent("");
    setCurrentAssistantMessageId(null);
  };

  const [error, setError] = useState<string | null>(null);

  return (
    <div className={cn("flex flex-col h-full bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
            <Brain className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold">AI Brainstorming Assistant</h1>
            <p className="text-sm text-muted-foreground">Generate ideas, create PRDs, and innovate</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {MODELS.map(model => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={exportChat}>
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Export chat</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={resetChat}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Reset chat</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          {isProcessing && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="destructive" size="sm" onClick={handleCancelExecution}>
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Cancel execution</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>
      </div>

      {/* Chat Area */}
      <ScrollArea className="flex-1 p-4" ref={scrollRef}>
        <div className="space-y-4 max-w-4xl mx-auto">
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "flex gap-3",
                message.type === 'user' ? "justify-end" : "justify-start"
              )}
            >
              {message.type !== 'user' && (
                <div className={cn(
                  "p-2 rounded-full",
                  message.type === 'system' ? "bg-blue-100 dark:bg-blue-900" : "bg-purple-100 dark:bg-purple-900"
                )}>
                  {message.type === 'system' ? (
                    <MessageSquare className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  ) : (
                    <Bot className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                  )}
                </div>
              )}
              
              <Card className={cn(
                "max-w-2xl",
                message.type === 'user' 
                  ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white" 
                  : "bg-white dark:bg-gray-800"
              )}>
                <CardContent className="p-4">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    {(message.id === currentAssistantMessageId && streamingContent
                      ? streamingContent
                      : message.content
                    ).split('\n').map((line, i) => (
                      <p key={i} className={message.type === 'user' ? "text-white" : ""}>
                        {line}
                      </p>
                    ))}
                  </div>
                  
                  {message.type === 'assistant' && (
                    <div className="flex items-center gap-2 mt-3 pt-3 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(message.content)}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Badge variant="secondary" className="text-xs">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </Badge>
                    </div>
                  )}
                  
                  {message.suggestions && (
                    <div className="mt-4 pt-4 border-t">
                      <p className="text-sm font-medium mb-2">💡 Follow-up suggestions:</p>
                      <div className="flex flex-wrap gap-2">
                        {message.suggestions.map((suggestion, i) => (
                          <Button
                            key={i}
                            variant="outline"
                            size="sm"
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="text-xs"
                          >
                            {suggestion}
                          </Button>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {message.type === 'user' && (
                <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
                  <User className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
              )}
            </motion.div>
          ))}
          
          {isProcessing && !currentAssistantMessageId && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex gap-3 justify-start"
            >
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-full">
                <Bot className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <Card className="bg-white dark:bg-gray-800">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Brainstorming ideas...</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-center"
            >
              <Card className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800">
                <CardContent className="p-4">
                  <p className="text-red-600 dark:text-red-400">{error}</p>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </div>
      </ScrollArea>

      {/* Brainstorming Choices */}
      <AnimatePresence>
        {showChoices && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-4"
          >
            <div className="max-w-4xl mx-auto">
              <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                Choose your brainstorming focus:
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                {BRAINSTORMING_CHOICES.map((choice) => {
                  const Icon = choice.icon;
                  return (
                    <Button
                      key={choice.id}
                      variant="outline"
                      className="h-auto p-3 flex flex-col items-start gap-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 dark:hover:from-blue-900/20 dark:hover:to-purple-900/20"
                      onClick={() => handleChoiceSelect(choice)}
                    >
                      <div className="flex items-center gap-2 w-full">
                        <Icon className="h-4 w-4" />
                        <span className="text-sm font-medium">{choice.title}</span>
                      </div>
                      <p className="text-xs text-muted-foreground text-left">
                        {choice.description}
                      </p>
                    </Button>
                  );
                })}
              </div>
              
              <div className="mb-3">
                <p className="text-xs text-muted-foreground mb-2">💡 Quick starters:</p>
                <div className="flex flex-wrap gap-2">
                  {QUICK_STARTERS.map((starter, i) => (
                    <Button
                      key={i}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleQuickStarter(starter)}
                      className="text-xs h-6 px-2"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      {starter}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Input Area */}
      <div className="border-t bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex gap-3">
            <div className="flex-1">
              <Textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Describe what you'd like to brainstorm, or choose an option above..."
                className="min-h-[60px] resize-none"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
                    e.preventDefault();
                    handleSubmit();
                  }
                }}
              />
            </div>
            <Button
              onClick={handleSubmit}
              disabled={!input.trim() || isProcessing}
              className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
            >
              {isProcessing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Press Cmd/Ctrl + Enter to send • Choose from options above for guided brainstorming
          </p>
        </div>
      </div>
    </div>
  );
};