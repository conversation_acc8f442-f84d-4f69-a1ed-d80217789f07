/**
 * Refactored ClaudeCodeSession component using the new session manager architecture
 * 
 * This is a simplified version that demonstrates the new centralized session management
 * approach while maintaining compatibility with the existing interface.
 */

import React, { useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ArrowLeft,
  Terminal,
  FolderOpen,
  Copy,
  Settings,
  X,
  Hash,
  Command
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { open } from "@tauri-apps/plugin-dialog";
import { useSessionManager } from "@/hooks/useSessionManager";
import { StreamMessage } from "./StreamMessage";
import { FloatingPromptInput, type FloatingPromptInputRef } from "./FloatingPromptInput";
import { ErrorBoundary } from "./ErrorBoundary";
import type { Session } from "@/lib/api";
import type { ModelType } from "@/types/session";

interface ClaudeCodeSessionProps {
  /**
   * Optional session to resume (when clicking from SessionList)
   */
  session?: Session;
  /**
   * Initial project path (for new sessions)
   */
  initialProjectPath?: string;
  /**
   * Callback to go back
   */
  onBack: () => void;
  /**
   * Callback to open hooks configuration
   */
  onProjectSettings?: (projectPath: string) => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when streaming state changes
   */
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
  /**
   * Whether the tab is currently active/visible
   */
  isActive?: boolean;
}

/**
 * Refactored ClaudeCodeSession component
 */
export const ClaudeCodeSessionRefactored: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  onBack,
  onProjectSettings,
  className,
  onStreamingChange,
  isActive = true,
}) => {
  const {
    sessionState,
    currentSession,
    isLoading,
    isStreaming,
    error,
    createSession,
    resumeSession,
    terminateSession,
    sendPrompt,
    queuePrompt,
    cancelExecution,
    clearError
  } = useSessionManager();

  const floatingPromptRef = React.useRef<FloatingPromptInputRef>(null);

  // Initialize session on mount
  useEffect(() => {
    const initializeSession = async () => {
      try {
        if (session) {
          // Resume existing session
          await resumeSession({
            sessionId: session.id,
            projectId: session.project_id,
            projectPath: session.project_path
          });
        } else if (initialProjectPath) {
          // Create new session with initial project path
          await createSession({
            projectPath: initialProjectPath
          });
        }
      } catch (err) {
        console.error('Failed to initialize session:', err);
      }
    };

    initializeSession();

    // Cleanup on unmount
    return () => {
      if (currentSession) {
        terminateSession().catch(console.error);
      }
    };
  }, [session, initialProjectPath]);

  // Report streaming state changes
  useEffect(() => {
    onStreamingChange?.(isStreaming, currentSession?.id || null);
  }, [isStreaming, currentSession?.id, onStreamingChange]);

  // Handle project path selection
  const handleSelectPath = useCallback(async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: "Select Project Directory"
      });
      
      if (selected && typeof selected === 'string') {
        await createSession({
          projectPath: selected
        });
      }
    } catch (err) {
      console.error("Failed to select directory:", err);
    }
  }, [createSession]);

  // Handle prompt submission
  const handleSendPrompt = useCallback(async (prompt: string, model: ModelType) => {
    if (!currentSession) {
      console.error('No active session');
      return;
    }

    try {
      await sendPrompt(prompt, model);
    } catch (err) {
      console.error('Failed to send prompt:', err);
    }
  }, [currentSession, sendPrompt]);

  // Handle execution cancellation
  const handleCancelExecution = useCallback(async () => {
    try {
      await cancelExecution();
    } catch (err) {
      console.error('Failed to cancel execution:', err);
    }
  }, [cancelExecution]);

  // Handle copy operations
  const handleCopyAsJsonl = useCallback(async () => {
    if (!currentSession) return;
    
    const jsonl = currentSession.messages
      .map(msg => JSON.stringify(msg))
      .join('\n');
    
    await navigator.clipboard.writeText(jsonl);
  }, [currentSession]);

  const handleCopyAsMarkdown = useCallback(async () => {
    if (!currentSession) return;
    
    let markdown = `# Claude Code Session\n\n`;
    markdown += `**Project:** ${currentSession.projectPath}\n`;
    markdown += `**Date:** ${new Date().toISOString()}\n\n`;
    markdown += `---\n\n`;

    for (const msg of currentSession.messages) {
      markdown += `## ${msg.type.charAt(0).toUpperCase() + msg.type.slice(1)}\n\n`;
      
      for (const content of msg.content) {
        if (content.type === 'text') {
          markdown += `${content.text}\n\n`;
        } else if (content.type === 'tool_use') {
          markdown += `### Tool: ${content.name}\n\n`;
          markdown += `\`\`\`json\n${JSON.stringify(content.input, null, 2)}\n\`\`\`\n\n`;
        } else if (content.type === 'tool_result') {
          markdown += `### Tool Result\n\n`;
          const contentText = typeof content.content === 'string' 
            ? content.content 
            : JSON.stringify(content.content, null, 2);
          markdown += `\`\`\`\n${contentText}\n\`\`\`\n\n`;
        }
      }
      
      if (msg.usage) {
        markdown += `*Tokens: ${msg.usage.input_tokens} in, ${msg.usage.output_tokens} out*\n\n`;
      }
    }

    await navigator.clipboard.writeText(markdown);
  }, [currentSession]);

  // Render project selection if no session
  if (!currentSession) {
    return (
      <div className={cn("flex flex-col h-full", className)}>
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <h1 className="text-lg font-semibold">New Claude Session</h1>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center">
          <div className="text-center space-y-4">
            <FolderOpen className="w-16 h-16 mx-auto text-muted-foreground" />
            <div>
              <h2 className="text-xl font-semibold mb-2">Select Project Directory</h2>
              <p className="text-muted-foreground mb-4">
                Choose a directory to start your Claude coding session
              </p>
              <Button onClick={handleSelectPath} size="lg">
                <FolderOpen className="w-4 h-4 mr-2" />
                Select Directory
              </Button>
            </div>
            {error && (
              <div className="text-red-500 text-sm mt-2">
                {error}
                <Button variant="ghost" size="sm" onClick={clearError} className="ml-2">
                  <X className="w-3 h-3" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className={cn("flex flex-col h-full", className)}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4" />
              <h1 className="text-lg font-semibold">Claude Session</h1>
              {currentSession.id && (
                <span className="text-xs text-muted-foreground font-mono">
                  {currentSession.id.slice(0, 8)}
                </span>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Token count */}
            <div className="text-sm text-muted-foreground">
              {currentSession.metadata.totalTokens.toLocaleString()} tokens
            </div>

            {/* Copy menu */}
            <div className="relative">
              <Button variant="ghost" size="sm">
                <Copy className="w-4 h-4" />
              </Button>
              {/* TODO: Add dropdown menu for copy options */}
            </div>

            {/* Settings */}
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>

            {/* Cancel execution */}
            {isStreaming && (
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={handleCancelExecution}
              >
                Cancel
              </Button>
            )}
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto p-4 space-y-4">
            <AnimatePresence>
              {currentSession.messages.map((message, index) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <StreamMessage
                    message={{
                      type: message.type,
                      message: {
                        content: message.content,
                        usage: message.usage
                      },
                      timestamp: message.timestamp,
                      metadata: message.metadata
                    }}
                    isStreaming={isStreaming && index === currentSession.messages.length - 1}
                  />
                </motion.div>
              ))}
            </AnimatePresence>

            {/* Streaming message */}
            {sessionState.streamingMessage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <StreamMessage
                  message={{
                    type: sessionState.streamingMessage.type || 'assistant',
                    message: {
                      content: sessionState.streamingMessage.content || [],
                      usage: sessionState.streamingMessage.usage
                    },
                    timestamp: sessionState.streamingMessage.timestamp || new Date().toISOString(),
                    metadata: sessionState.streamingMessage.metadata || {}
                  }}
                  isStreaming={true}
                />
              </motion.div>
            )}

            {/* Loading indicator */}
            {isLoading && !sessionState.streamingMessage && (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  <span>Claude is thinking...</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Queued prompts */}
        {currentSession.queuedPrompts.length > 0 && (
          <div className="border-t p-4">
            <div className="text-sm text-muted-foreground mb-2">
              Queued prompts ({currentSession.queuedPrompts.length})
            </div>
            <div className="space-y-2">
              {currentSession.queuedPrompts.slice(0, 3).map((prompt) => (
                <div key={prompt.id} className="text-sm bg-muted p-2 rounded truncate">
                  {prompt.prompt}
                </div>
              ))}
              {currentSession.queuedPrompts.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  +{currentSession.queuedPrompts.length - 3} more
                </div>
              )}
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className="border-t p-4 bg-red-50 border-red-200">
            <div className="flex items-center justify-between">
              <div className="text-red-700 text-sm">{error}</div>
              <Button variant="ghost" size="sm" onClick={clearError}>
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        )}

        {/* Input */}
        <div className="border-t p-4">
          <FloatingPromptInput
            ref={floatingPromptRef}
            onSendPrompt={handleSendPrompt}
            isLoading={isLoading}
            projectPath={currentSession.projectPath}
            placeholder="Ask Claude to help with your code..."
          />
        </div>
      </div>
    </ErrorBoundary>
  );
};