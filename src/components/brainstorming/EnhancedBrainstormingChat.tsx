/**
 * Enhanced Brainstorming Chat Component
 * 
 * This component extends the existing BrainstormingChat with idea extraction
 * and management capabilities, integrating the IdeaExtractor service and
 * IdeaManager component.
 */

import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Lightbulb,
  FileText,
  Target,
  Users,
  Sparkles,
  Bot,
  User,
  Loader2,
  Copy,
  Download,
  RefreshCw,
  Zap,
  Brain,
  X,
  SplitSquareVertical,
  BarChart,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { cn } from "@/lib/utils";
import { brainstormApi, type BrainstormMessage as ApiBrainstormMessage, type BrainstormStreamEvent, type BrainstormCompleteEvent, type BrainstormErrorEvent } from "@/lib/brainstorm-api";
import { useToast } from "@/hooks/useToast";
import { listen, type UnlistenFn } from "@tauri-apps/api/event";
import { useBrainstormStore } from "@/stores/brainstormStore";
import { IdeaManager } from "./IdeaManager";
import { ideaExtractor } from "@/services";
import { ChatMessage, Idea } from "@/types/brainstorm";
import { VisualizationContainer } from "./visualizations/VisualizationContainer";
import { universalPersistence } from "@/lib/universal-persistence";
import type { BrainstormSessionInfo } from "@/types/unified-session";

interface EnhancedBrainstormingChatProps {
  onClose?: () => void;
  onBack?: () => void;
  className?: string;
}

interface BrainstormingChoice {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: React.ComponentType<any>;
  category: 'idea' | 'prd' | 'analysis' | 'strategy';
}

const BRAINSTORMING_CHOICES: BrainstormingChoice[] = [
  {
    id: 'idea-generation',
    title: 'Generate Ideas',
    description: 'Brainstorm creative solutions and innovative concepts',
    prompt: 'Help me brainstorm creative ideas for: ',
    icon: Lightbulb,
    category: 'idea'
  },
  {
    id: 'prd-creation',
    title: 'Create PRD',
    description: 'Generate a comprehensive Product Requirements Document',
    prompt: 'Create a detailed PRD for: ',
    icon: FileText,
    category: 'prd'
  },
  {
    id: 'feature-analysis',
    title: 'Feature Analysis',
    description: 'Analyze features, pros/cons, and implementation approaches',
    prompt: 'Analyze the features and implementation for: ',
    icon: Target,
    category: 'analysis'
  },
  {
    id: 'user-personas',
    title: 'User Personas',
    description: 'Define target users and their needs',
    prompt: 'Create user personas for: ',
    icon: Users,
    category: 'strategy'
  },
  {
    id: 'innovation-workshop',
    title: 'Innovation Workshop',
    description: 'Structured creative thinking exercises',
    prompt: 'Run an innovation workshop for: ',
    icon: Sparkles,
    category: 'idea'
  },
];

export const EnhancedBrainstormingChat: React.FC<EnhancedBrainstormingChatProps> = ({
  onClose,
  className
}) => {
  // Use persistent session management
  const {
    currentSessionId,
    createSession,
    setCurrentSession,
    addMessage,
    updateMessage,
    getCurrentSession
  } = useBrainstormStore();
  
  const [sessionId, setSessionId] = useState<string>(
    currentSessionId || `brainstorm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  );
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedModel, setSelectedModel] = useState("sonnet");
  const [showChoices, setShowChoices] = useState(true);
  const [streamingContent, setStreamingContent] = useState<string>("");
  const [currentAssistantMessageId, setCurrentAssistantMessageId] = useState<string | null>(null);
  const [showIdeasPanel, setShowIdeasPanel] = useState(false);
  const [showVisualizationPanel, setShowVisualizationPanel] = useState(false);
  const [ideaPanelWidth] = useState(400); // Default width
  // const [selectedIdea, setSelectedIdea] = useState<Idea | null>(null);
  const [selectedIdeaIds, setSelectedIdeaIds] = useState<string[]>([]);
  
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const isListeningRef = useRef(false);
  const isMountedRef = useRef(true);
  const { toast } = useToast();

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages, streamingContent]);

  // Initialize session in store
  useEffect(() => {
    createSession("New Brainstorming Session");
  }, [createSession]);

  // Cleanup event listeners on unmount
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      console.log('[EnhancedBrainstormingChat] Component unmounting, cleaning up listeners');
      isMountedRef.current = false;
      isListeningRef.current = false;
      
      // Clean up listeners
      unlistenRefs.current.forEach(unlisten => unlisten());
      unlistenRefs.current = [];
    };
  }, []);

  const handleChoiceSelect = (choice: BrainstormingChoice) => {
    setInput(choice.prompt);
    setShowChoices(false);
    inputRef.current?.focus();
  };

  const handleQuickStarter = (starter: string) => {
    setInput(prev => prev + starter);
    inputRef.current?.focus();
  };

  const handleStreamMessage = (event: any) => {
    if (!isMountedRef.current) return;
    
    const data = event.payload as BrainstormStreamEvent;
    
    if (data.content) {
      setStreamingContent(data.accumulated || data.content);
    }
  };

  const setupEventListeners = () => {
    if (isListeningRef.current || !isMountedRef.current) return;
    
    console.log('[EnhancedBrainstormingChat] Setting up event listeners');

    // Listen to chat-specific events
    listen('chat-stream', handleStreamMessage).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });

    listen('chat-error', (event: any) => {
      if (!isMountedRef.current) return;
      const data = event.payload as BrainstormErrorEvent;
      console.error('[EnhancedBrainstormingChat] Chat error:', data.error);
      setError(data.error || 'An error occurred');
      setIsProcessing(false);
      
      // Remove the placeholder message on error
      if (currentAssistantMessageId) {
        setMessages(prev => prev.filter(msg => msg.id !== currentAssistantMessageId));
        setCurrentAssistantMessageId(null);
      }
    }).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });

    listen('chat-complete', (event: any) => {
      if (!isMountedRef.current) return;
      const data = event.payload as BrainstormCompleteEvent;
      console.log('[EnhancedBrainstormingChat] Chat complete');
      
      // Finalize the streaming content into a message
      if (currentAssistantMessageId) {
        const finalContent = data.content || streamingContent;
        
        // Extract ideas from the assistant's message
        const extractedIdeas = processAssistantMessage(currentAssistantMessageId, finalContent);
        
        setMessages(prev => prev.map(msg => 
          msg.id === currentAssistantMessageId 
            ? { 
                ...msg, 
                content: finalContent, 
                status: 'success', 
                suggestions: generateFollowUpSuggestions(),
                extractedIdeas: extractedIdeas
              }
            : msg
        ));
        
        // Update message in store
        updateMessage(sessionId, currentAssistantMessageId, {
          content: finalContent,
          status: 'success',
          extractedIdeas: extractedIdeas
        });
        
        setStreamingContent("");
        setCurrentAssistantMessageId(null);
        
        // Show ideas panel if ideas were extracted
        if (extractedIdeas.length > 0 && !showIdeasPanel) {
          setShowIdeasPanel(true);
          toast({
            message: `${extractedIdeas.length} idea${extractedIdeas.length === 1 ? '' : 's'} extracted!`,
            type: "success"
          });
        }
      }
      
      setIsProcessing(false);
    }).then(unlisten => {
      unlistenRefs.current.push(unlisten);
    });
    
    isListeningRef.current = true;
  };

  const processAssistantMessage = (messageId: string, content: string): string[] => {
    // Create a temporary message object to pass to the extractor
    const tempMessage: ChatMessage = {
      id: messageId,
      type: 'assistant',
      content: content,
      timestamp: new Date().toISOString()
    };
    
    // Process the message to extract and store ideas
    return ideaExtractor.processMessage(tempMessage);
  };

  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return;
    
    const userMessageId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const assistantMessageId = `assistant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Add user message
    const userMessage: ChatMessage = {
      id: userMessageId,
      type: 'user',
      content: input.trim(),
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    addMessage(sessionId, {
      type: 'user',
      content: input.trim()
    });
    
    // Clear input and hide choices
    setInput("");
    setShowChoices(false);
    
    // Add assistant placeholder message
    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      type: 'assistant',
      content: "",
      timestamp: new Date().toISOString(),
      status: 'pending'
    };
    
    setMessages(prev => [...prev, assistantMessage]);
    setCurrentAssistantMessageId(assistantMessageId);
    addMessage(sessionId, {
      type: 'assistant',
      content: "",
      status: 'pending'
    });
    
    // Setup event listeners if not already set up
    setupEventListeners();
    
    // Start processing
    setIsProcessing(true);
    
    try {
      // Convert messages to API format
      const apiMessages: ApiBrainstormMessage[] = messages
        .filter(m => m.type !== 'system')
        .concat(userMessage)
        .map(m => ({
          role: m.type,
          content: m.content
        }));
      
      // Send to API
      await brainstormApi.chat({
        sessionId,
        messages: apiMessages,
        model: selectedModel,
        temperature: 0.7,
        maxTokens: 2000
      });
    } catch (error) {
      console.error('[EnhancedBrainstormingChat] Error sending message:', error);
      setError('Failed to send message. Please try again.');
      
      // Remove the placeholder message
      setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
      setCurrentAssistantMessageId(null);
      setIsProcessing(false);
    }
  };

  const handleCancelExecution = async () => {
    try {
      await brainstormApi.cancel(sessionId);
      console.log('[EnhancedBrainstormingChat] Execution cancelled');
      
      // Update the current assistant message if it exists
      if (currentAssistantMessageId) {
        setMessages(prev => prev.map(msg => 
          msg.id === currentAssistantMessageId
            ? { ...msg, content: streamingContent + "\n\n[Execution cancelled]" }
            : msg
        ));
        setCurrentAssistantMessageId(null);
      }
      
      setStreamingContent("");
      setIsProcessing(false);
    } catch (error) {
      console.error('[EnhancedBrainstormingChat] Error cancelling execution:', error);
    }
  };

  const setError = (errorMessage: string) => {
    toast({
      message: errorMessage,
      type: "error"
    });
  };

  const generateFollowUpSuggestions = (): string[] => {
    // Generate contextual suggestions based on the response content
    const suggestions = [
      "Create a detailed user journey map",
      "Develop a competitive analysis",
      "Design a technical architecture",
      "Plan a go-to-market strategy",
      "Create wireframes and mockups",
      "Explore implementation details",
      "Analyze potential risks",
      "Define success metrics"
    ];
    
    // Return a random subset of suggestions
    return suggestions.sort(() => 0.5 - Math.random()).slice(0, 5);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
    inputRef.current?.focus();
  };

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      message: "Content copied to clipboard",
      type: "success"
    });
  };

  const exportChat = () => {
    const chatContent = messages
      .filter(m => m.type !== 'system' || m.id === 'welcome')
      .map(m => `**${m.type.toUpperCase()}** (${new Date(m.timestamp).toLocaleString()})\n${m.content}\n`)
      .join('\n---\n\n');
    
    const blob = new Blob([chatContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `brainstorming-session-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const resetChat = async () => {
    // Clean up any active session
    if (isProcessing) {
      await handleCancelExecution();
    }
    
    // Clean up listeners
    unlistenRefs.current.forEach(unlisten => unlisten());
    unlistenRefs.current = [];
    isListeningRef.current = false;
    
    setMessages([
      {
        id: 'welcome',
        type: 'system',
        content: "🚀 Welcome to your Enhanced AI Brainstorming Assistant! I'm here to help you generate ideas, create PRDs, and turn your concepts into actionable plans. Choose a brainstorming type below or describe what you'd like to explore.",
        timestamp: new Date().toISOString(),
        extractedIdeas: []
      }
    ]);
    setShowChoices(true);
    setStreamingContent("");
    setCurrentAssistantMessageId(null);
  };

  const handleIdeaClick = (idea: Idea) => {
    // Update selected idea IDs for visualization components
    setSelectedIdeaIds(prev => {
      const ideaId = idea.id;
      if (prev.includes(ideaId)) {
        return prev.filter(id => id !== ideaId);
      } else {
        return [...prev, ideaId];
      }
    });
  };
  
  const toggleVisualizationPanel = () => {
    setShowVisualizationPanel(!showVisualizationPanel);
  };

  const toggleIdeasPanel = () => {
    setShowIdeasPanel(!showIdeasPanel);
  };

  return (
    <div className={cn("flex h-full", className)}>
      {/* Main chat area */}
      <div className={cn(
        "flex flex-col h-full transition-all duration-300", 
        showIdeasPanel ? "w-[calc(100%-400px)]" : "w-full"
      )}>
        {/* Chat header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Enhanced Brainstorming</h2>
          </div>
          
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={toggleIdeasPanel}>
                    {showIdeasPanel ? <SplitSquareVertical className="h-4 w-4" /> : <Lightbulb className="h-4 w-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {showIdeasPanel ? "Hide Ideas Panel" : "Show Ideas Panel"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={toggleVisualizationPanel}>
                    <BarChart className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {showVisualizationPanel ? "Hide Visualizations" : "Show Visualizations"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="sonnet">Claude Sonnet</SelectItem>
                <SelectItem value="haiku">Claude Haiku</SelectItem>
                <SelectItem value="opus">Claude Opus</SelectItem>
              </SelectContent>
            </Select>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={resetChat}>
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Reset Chat
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={exportChat}>
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  Export Chat
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        
        {/* Chat messages */}
        <ScrollArea ref={scrollRef} className="flex-1 p-4">
          <div className="space-y-4 pb-20">
            <AnimatePresence initial={false}>
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className={cn(
                    "overflow-hidden",
                    message.type === 'system' && "bg-muted/50 border-muted",
                    message.type === 'user' && "bg-primary/10 border-primary/20",
                    message.type === 'assistant' && "bg-secondary/10 border-secondary/20"
                  )}>
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="mt-1">
                          {message.type === 'user' ? (
                            <div className="bg-primary text-primary-foreground p-1.5 rounded-md">
                              <User className="h-4 w-4" />
                            </div>
                          ) : message.type === 'assistant' ? (
                            <div className="bg-secondary text-secondary-foreground p-1.5 rounded-md">
                              <Bot className="h-4 w-4" />
                            </div>
                          ) : (
                            <div className="bg-muted-foreground text-muted p-1.5 rounded-md">
                              <Zap className="h-4 w-4" />
                            </div>
                          )}
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          {message.status === 'pending' ? (
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Generating response...</span>
                            </div>
                          ) : (
                            <div className="prose dark:prose-invert max-w-none">
                              {message.content}
                              
                              {/* Show extracted ideas badges if any */}
                              {message.extractedIdeas && message.extractedIdeas.length > 0 && (
                                <div className="mt-2 flex flex-wrap gap-1">
                                  <Badge variant="outline" className="flex items-center gap-1">
                                    <Lightbulb className="h-3 w-3" />
                                    <span>{message.extractedIdeas.length} idea{message.extractedIdeas.length !== 1 ? 's' : ''} extracted</span>
                                  </Badge>
                                </div>
                              )}
                            </div>
                          )}
                          
                          {/* Message actions */}
                          {message.type !== 'system' && message.status !== 'pending' && (
                            <div className="flex items-center gap-2 pt-2">
                              <Button variant="ghost" size="sm" onClick={() => copyToClipboard(message.content)}>
                                <Copy className="h-3 w-3 mr-1" />
                                Copy
                              </Button>
                            </div>
                          )}
                          
                          {/* Suggestions */}
                          {message.type === 'assistant' && message.suggestions && message.suggestions.length > 0 && (
                            <div className="flex flex-wrap gap-2 pt-2">
                              {message.suggestions.map((suggestion, i) => (
                                <Badge 
                                  key={i} 
                                  variant="outline" 
                                  className="cursor-pointer hover:bg-accent"
                                  onClick={() => handleSuggestionClick(suggestion)}
                                >
                                  {suggestion}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
              
              {/* Streaming content */}
              {streamingContent && currentAssistantMessageId && (
                <motion.div
                  key="streaming"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="bg-secondary/10 border-secondary/20 overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="mt-1">
                          <div className="bg-secondary text-secondary-foreground p-1.5 rounded-md">
                            <Bot className="h-4 w-4" />
                          </div>
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="prose dark:prose-invert max-w-none">
                            {streamingContent}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </ScrollArea>
        
        {/* Visualization panel */}
        {showVisualizationPanel && (
          <div className="border-t">
            <VisualizationContainer
              sessionId={sessionId}
              ideas={useBrainstormStore.getState().getIdeasBySession(sessionId) || []}
              selectedIdeaIds={selectedIdeaIds}
              onIdeaSelect={(ideaId) => {
                const idea = useBrainstormStore.getState().getIdeaById(ideaId);
                if (idea) {
                  handleIdeaClick(idea);
                }
              }}
              className="w-full"
            />
          </div>
        )}
        
        {/* Brainstorming choices */}
        {showChoices && (
          <div className="p-4 border-t">
            <h3 className="text-sm font-medium mb-2">Choose a brainstorming type:</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
              {BRAINSTORMING_CHOICES.map((choice) => (
                <Card 
                  key={choice.id} 
                  className="cursor-pointer hover:bg-accent transition-colors"
                  onClick={() => handleChoiceSelect(choice)}
                >
                  <CardContent className="p-3 flex flex-col items-center text-center">
                    <choice.icon className="h-6 w-6 mb-2" />
                    <h4 className="text-sm font-medium">{choice.title}</h4>
                    <p className="text-xs text-muted-foreground">{choice.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
        
        {/* Input area */}
        <div className="p-4 border-t">
          <div className="flex items-end gap-2">
            <Textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Type your message..."
              className="min-h-[80px]"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button 
              onClick={isProcessing ? handleCancelExecution : handleSendMessage}
              disabled={isProcessing && !streamingContent}
              className="mb-1"
            >
              {isProcessing ? (
                streamingContent ? <X className="h-4 w-4" /> : <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {/* Quick starters */}
          <div className="flex flex-wrap gap-2 mt-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleQuickStarter("What are the key features for ")}
            >
              Key Features
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleQuickStarter("How can we improve ")}
            >
              Improvements
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleQuickStarter("What are the pros and cons of ")}
            >
              Pros & Cons
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleQuickStarter("Create a project timeline for ")}
            >
              Timeline
            </Button>
          </div>
        </div>
      </div>
      
      {/* Ideas panel */}
      {showIdeasPanel && (
        <div 
          className="border-l h-full w-[400px] flex flex-col transition-all duration-300"
          style={{ width: `${ideaPanelWidth}px` }}
        >
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              <h2 className="text-lg font-semibold">Ideas</h2>
            </div>
            <Button variant="ghost" size="icon" onClick={toggleIdeasPanel}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex-1 overflow-auto">
            <IdeaManager 
              sessionId={sessionId} 
              onIdeaClick={handleIdeaClick}
            />
          </div>
        </div>
      )}
    </div>
  );
};