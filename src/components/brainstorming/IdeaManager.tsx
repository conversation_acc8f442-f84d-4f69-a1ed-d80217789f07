/**
 * IdeaManager Component
 * 
 * This component provides a UI for managing extracted ideas, including
 * viewing, editing, deleting, and changing the status of ideas.
 */

import React, { useState, useEffect } from 'react';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { Idea, IdeaStatus, Priority } from '@/types/brainstorm';
import { ideaExtractor } from '@/services';

// UI components
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Icons
import {
  Pencil,
  Trash2,
  Tag,
  Link,
  MoreVertical,
  Check,
  X,

  ArrowUpDown,
} from 'lucide-react';

interface IdeaManagerProps {
  sessionId: string | null;
  onIdeaClick?: (idea: Idea) => void;
  onIdeaEdit?: (idea: Idea) => void;
  onIdeaDelete?: (ideaId: string) => void;
}

export const IdeaManager: React.FC<IdeaManagerProps> = ({
  sessionId,
  onIdeaClick,
  onIdeaEdit,
  onIdeaDelete,
}) => {
  const {
    ideas,
    getIdeasBySession,
    updateIdea,
    deleteIdea,

    // removeIdeaFromCluster,
  } = useBrainstormStore();

  const [sessionIdeas, setSessionIdeas] = useState<Idea[]>([]);
  const [editingIdeaId, setEditingIdeaId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [sortBy, setSortBy] = useState<'createdAt' | 'status' | 'priority'>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filter, setFilter] = useState<IdeaStatus | 'all'>('all');
  const [newTag, setNewTag] = useState('');

  // Load ideas when session changes
  useEffect(() => {
    if (sessionId) {
      const ideas = getIdeasBySession(sessionId);
      setSessionIdeas(ideas);
    } else {
      setSessionIdeas([]);
    }
  }, [sessionId, ideas, getIdeasBySession]);

  // Sort and filter ideas
  const sortedAndFilteredIdeas = sessionIdeas
    .filter(idea => filter === 'all' || idea.status === filter)
    .sort((a, b) => {
      if (sortBy === 'createdAt') {
        return sortDirection === 'asc'
          ? new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          : new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } else if (sortBy === 'status') {
        return sortDirection === 'asc'
          ? a.status.localeCompare(b.status)
          : b.status.localeCompare(a.status);
      } else if (sortBy === 'priority') {
        const priorityOrder = {
          [Priority.LOW]: 1,
          [Priority.MEDIUM]: 2,
          [Priority.HIGH]: 3,
          [Priority.CRITICAL]: 4,
        };
        const aPriority = a.priority ? priorityOrder[a.priority] : 0;
        const bPriority = b.priority ? priorityOrder[b.priority] : 0;
        return sortDirection === 'asc' ? aPriority - bPriority : bPriority - aPriority;
      }
      return 0;
    });

  // Handle idea status change
  const handleStatusChange = (ideaId: string, status: IdeaStatus) => {
    updateIdea(ideaId, { status });
  };

  // Handle idea priority change
  const handlePriorityChange = (ideaId: string, priority: Priority) => {
    updateIdea(ideaId, { priority });
  };

  // Start editing an idea
  const startEditing = (idea: Idea) => {
    setEditingIdeaId(idea.id);
    setEditContent(idea.content);
  };

  // Save edited idea
  const saveEdit = (ideaId: string) => {
    updateIdea(ideaId, { content: editContent });
    setEditingIdeaId(null);
    setEditContent('');
    
    // Update connections after editing
    ideaExtractor.updateConnections(ideaId);
    
    if (onIdeaEdit && ideas[ideaId]) {
      onIdeaEdit(ideas[ideaId]);
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingIdeaId(null);
    setEditContent('');
  };

  // Handle idea deletion
  const handleDeleteIdea = (ideaId: string) => {
    deleteIdea(ideaId);
    if (onIdeaDelete) {
      onIdeaDelete(ideaId);
    }
  };

  // Add a tag to an idea
  const addTag = (ideaId: string, tag: string) => {
    const idea = ideas[ideaId];
    if (idea && tag && !idea.tags.includes(tag)) {
      updateIdea(ideaId, {
        tags: [...idea.tags, tag],
      });
    }
    setNewTag('');
  };

  // Remove a tag from an idea
  const removeTag = (ideaId: string, tagToRemove: string) => {
    const idea = ideas[ideaId];
    if (idea) {
      updateIdea(ideaId, {
        tags: idea.tags.filter(tag => tag !== tagToRemove),
      });
    }
  };

  // Toggle sort direction
  const toggleSortDirection = () => {
    setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
  };

  return (
    <div className="idea-manager">
      <div className="idea-manager-header">
        <h2 className="text-xl font-semibold mb-4">Ideas ({sortedAndFilteredIdeas.length})</h2>
        
        <div className="flex items-center gap-2 mb-4">
          <Select
            value={filter}
            onValueChange={(value) => setFilter(value as IdeaStatus | 'all')}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value={IdeaStatus.TO_EXPLORE}>To Explore</SelectItem>
              <SelectItem value={IdeaStatus.IN_PROGRESS}>In Progress</SelectItem>
              <SelectItem value={IdeaStatus.VALIDATED}>Validated</SelectItem>
              <SelectItem value={IdeaStatus.ARCHIVED}>Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Select
            value={sortBy}
            onValueChange={(value) => setSortBy(value as 'createdAt' | 'status' | 'priority')}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="createdAt">Date Created</SelectItem>
              <SelectItem value="status">Status</SelectItem>
              <SelectItem value="priority">Priority</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="icon" onClick={toggleSortDirection}>
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="idea-list space-y-4">
        {sortedAndFilteredIdeas.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No ideas found. Ideas will appear here when extracted from chat messages.
          </div>
        ) : (
          sortedAndFilteredIdeas.map((idea) => (
            <Card key={idea.id} className="idea-card">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    {editingIdeaId === idea.id ? (
                      <Input
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="mb-2"
                        autoFocus
                      />
                    ) : (
                      <CardTitle 
                        className="text-lg cursor-pointer hover:text-primary"
                        onClick={() => onIdeaClick && onIdeaClick(idea)}
                      >
                        {idea.content}
                      </CardTitle>
                    )}
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => startEditing(idea)}>
                        <Pencil className="mr-2 h-4 w-4" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDeleteIdea(idea.id)}>
                        <Trash2 className="mr-2 h-4 w-4" /> Delete
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel>Status</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleStatusChange(idea.id, IdeaStatus.TO_EXPLORE)}>
                        {idea.status === IdeaStatus.TO_EXPLORE && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.status === IdeaStatus.TO_EXPLORE ? 'font-bold' : ''}>To Explore</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(idea.id, IdeaStatus.IN_PROGRESS)}>
                        {idea.status === IdeaStatus.IN_PROGRESS && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.status === IdeaStatus.IN_PROGRESS ? 'font-bold' : ''}>In Progress</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(idea.id, IdeaStatus.VALIDATED)}>
                        {idea.status === IdeaStatus.VALIDATED && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.status === IdeaStatus.VALIDATED ? 'font-bold' : ''}>Validated</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleStatusChange(idea.id, IdeaStatus.ARCHIVED)}>
                        {idea.status === IdeaStatus.ARCHIVED && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.status === IdeaStatus.ARCHIVED ? 'font-bold' : ''}>Archived</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuLabel>Priority</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handlePriorityChange(idea.id, Priority.LOW)}>
                        {idea.priority === Priority.LOW && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.priority === Priority.LOW ? 'font-bold' : ''}>Low</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePriorityChange(idea.id, Priority.MEDIUM)}>
                        {idea.priority === Priority.MEDIUM && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.priority === Priority.MEDIUM ? 'font-bold' : ''}>Medium</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePriorityChange(idea.id, Priority.HIGH)}>
                        {idea.priority === Priority.HIGH && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.priority === Priority.HIGH ? 'font-bold' : ''}>High</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handlePriorityChange(idea.id, Priority.CRITICAL)}>
                        {idea.priority === Priority.CRITICAL && <Check className="mr-2 h-4 w-4" />}
                        <span className={idea.priority === Priority.CRITICAL ? 'font-bold' : ''}>Critical</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <CardDescription className="flex items-center gap-2 mt-1">
                  <Badge variant={getStatusVariant(idea.status)}>
                    {formatStatus(idea.status)}
                  </Badge>
                  
                  {idea.priority && (
                    <Badge variant={getPriorityVariant(idea.priority)}>
                      {formatPriority(idea.priority)}
                    </Badge>
                  )}
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {editingIdeaId === idea.id ? (
                  <div className="flex gap-2">
                    <Button size="sm" onClick={() => saveEdit(idea.id)}>
                      <Check className="h-4 w-4 mr-1" /> Save
                    </Button>
                    <Button size="sm" variant="outline" onClick={cancelEdit}>
                      <X className="h-4 w-4 mr-1" /> Cancel
                    </Button>
                  </div>
                ) : (
                  <div className="tags-container">
                    <div className="flex flex-wrap gap-1 mb-2">
                      {idea.tags.map((tag) => (
                        <Badge 
                          key={tag} 
                          variant="outline"
                          className="flex items-center gap-1"
                        >
                          <span>{tag}</span>
                          <button 
                            onClick={() => removeTag(idea.id, tag)}
                            className="ml-1 text-muted-foreground hover:text-foreground"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </Badge>
                      ))}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Add tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        className="h-8"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && newTag) {
                            addTag(idea.id, newTag);
                            e.preventDefault();
                          }
                        }}
                      />
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => addTag(idea.id, newTag)}
                        disabled={!newTag}
                      >
                        <Tag className="h-3 w-3 mr-1" /> Add
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
              
              <CardFooter className="pt-0">
                <div className="text-xs text-muted-foreground">
                  {idea.connections.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Link className="h-3 w-3" />
                      <span>Connected to {idea.connections.length} other ideas</span>
                    </div>
                  )}
                </div>
              </CardFooter>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

// Helper functions
function formatStatus(status: IdeaStatus): string {
  switch (status) {
    case IdeaStatus.TO_EXPLORE:
      return 'To Explore';
    case IdeaStatus.IN_PROGRESS:
      return 'In Progress';
    case IdeaStatus.VALIDATED:
      return 'Validated';
    case IdeaStatus.ARCHIVED:
      return 'Archived';
    default:
      return status;
  }
}

export function formatIdeaStatus(status: IdeaStatus): string {
  switch (status) {
    case IdeaStatus.TO_EXPLORE:
      return 'To Explore';
    case IdeaStatus.IN_PROGRESS:
      return 'In Progress';
    case IdeaStatus.VALIDATED:
      return 'Validated';
    case IdeaStatus.ARCHIVED:
      return 'Archived';
    default:
      return status;
  }
}

export function getStatusVariant(status: IdeaStatus): 'default' | 'secondary' | 'outline' | 'destructive' {
  switch (status) {
    case IdeaStatus.TO_EXPLORE:
      return 'default';
    case IdeaStatus.IN_PROGRESS:
      return 'secondary';
    case IdeaStatus.VALIDATED:
      return 'outline';
    case IdeaStatus.ARCHIVED:
      return 'destructive';
    default:
      return 'default';
  }
}

function formatPriority(priority: Priority): string {
  switch (priority) {
    case Priority.LOW:
      return 'Low';
    case Priority.MEDIUM:
      return 'Medium';
    case Priority.HIGH:
      return 'High';
    case Priority.CRITICAL:
      return 'Critical';
    default:
      return priority;
  }
}

function getPriorityVariant(priority: Priority): 'default' | 'secondary' | 'outline' | 'destructive' {
  switch (priority) {
    case Priority.LOW:
      return 'outline';
    case Priority.MEDIUM:
      return 'default';
    case Priority.HIGH:
      return 'secondary';
    case Priority.CRITICAL:
      return 'destructive';
    default:
      return 'default';
  }
}