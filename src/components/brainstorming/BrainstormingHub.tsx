import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  GitBranch,
  Columns3,
  Grid3x3,
  Settings,
  Save,
  Share2,
  FileText,
  X,
  Loader2,
  ListTodo,
  Download,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBrainstormStore } from '@/stores/brainstormStore';
import { ViewType } from '@/types/brainstorm';
import { EnhancedBrainstormingChat } from './EnhancedBrainstormingChat';
import { MindMapView } from './MindMapView';
import { KanbanView } from './KanbanView';
import { MatrixView } from './MatrixView';
import { TaskGenerator } from './TaskGenerator';
import { MultiModalInput } from './MultiModalInput';
import { BrainstormErrorBoundary } from './BrainstormErrorBoundary';
import { TemplatesView } from './TemplatesView';
import { useToast } from '@/hooks/useToast';
import { useBrainstormStorage } from '@/hooks/useBrainstormStorage';

interface BrainstormingHubProps {
  sessionId?: string;
  onClose?: () => void;
  className?: string;
}

interface ViewConfig {
  id: ViewType;
  label: string;
  icon: React.ComponentType<any>;
  component?: React.ComponentType<any>;
  disabled?: boolean;
}

const viewConfigs: ViewConfig[] = [
  {
    id: ViewType.CHAT,
    label: 'Chat',
    icon: MessageSquare,
    component: EnhancedBrainstormingChat,
  },
  {
    id: ViewType.MIND_MAP,
    label: 'Mind Map',
    icon: GitBranch,
    component: MindMapView,
  },
  {
    id: ViewType.KANBAN,
    label: 'Kanban',
    icon: Columns3,
    component: KanbanView,
  },
  {
    id: ViewType.MATRIX,
    label: 'Matrix',
    icon: Grid3x3,
    component: MatrixView,
  },
  {
    id: 'tasks' as ViewType,
    label: 'Tasks',
    icon: ListTodo,
    component: TaskGenerator,
  },
  {
    id: ViewType.TEMPLATES,
    label: 'Templates',
    icon: FileText,
    component: TemplatesView,
  },
];

export const BrainstormingHub: React.FC<BrainstormingHubProps> = ({
  sessionId: initialSessionId,
  onClose,
  className,
}) => {
  const { 
    currentSessionId, 
    createSession, 
    getCurrentSession,
    getIdeasBySession,
    currentView,
    setCurrentView,
    exportSession,
  } = useBrainstormStore();

  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);
  
  // Initialize persistent storage
  const { saveAll, createBackup } = useBrainstormStorage();

  // Use provided sessionId or current session, create new if none exists
  const sessionId = initialSessionId || currentSessionId || (() => {
    const newId = createSession('New Brainstorming Session');
    return newId;
  })();

  const session = getCurrentSession();
  const ideas = getIdeasBySession(sessionId);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await exportSession(sessionId, 'markdown');
      toast({
        message: 'Session exported successfully',
        type: 'success',
      });
    } catch (error) {
      toast({
        message: 'Failed to export session',
        type: 'error',
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleSave = async () => {
    try {
      // Save all brainstorming data to file system
      await saveAll();
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  };

  const handleMediaAdd = (attachmentId: string, type: 'image' | 'audio' | 'sketch') => {
    // Media is already saved by MultiModalInput
    // Here we could update the current idea or session with the attachment
    console.log(`Added ${type} attachment: ${attachmentId}`);
  };

  const handleAttachmentsUpdate = (attachmentIds: string[]) => {
    // Update the current idea or session with all attachment IDs
    console.log('Updated attachments:', attachmentIds);
  };

  return (
    <BrainstormErrorBoundary>
      <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold">
            {session?.title || 'Brainstorming Session'}
          </h2>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {ideas.length} ideas
            </Badge>
            <Badge variant="outline">
              {session?.metadata.totalClusters || 0} clusters
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <MultiModalInput
            ideaId={session?.metadata.lastActiveIdeaId || sessionId}
            onImageAdd={(id) => handleMediaAdd(id, 'image')}
            onAudioAdd={(id) => handleMediaAdd(id, 'audio')}
            onSketchAdd={(id) => handleMediaAdd(id, 'sketch')}
            onAttachmentsUpdate={handleAttachmentsUpdate}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            disabled={isExporting}
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Share2 className="h-4 w-4 mr-1" />
            )}
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              try {
                await createBackup();
              } catch (error) {
                console.error('Failed to create backup:', error);
              }
            }}
            title="Create backup"
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
          >
            <Settings className="h-4 w-4" />
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* View Tabs */}
      <Tabs
        value={currentView}
        onValueChange={(value) => setCurrentView(value as ViewType)}
        className="flex-1 flex flex-col"
      >
        <TabsList className="w-full justify-start rounded-none border-b h-auto p-0">
          {viewConfigs.map((config) => {
            const Icon = config.icon;
            return (
              <TabsTrigger
                key={config.id}
                value={config.id}
                disabled={config.disabled}
                className="rounded-none border-b-2 border-transparent data-[state=active]:border-primary px-4 py-3"
              >
                <Icon className="h-4 w-4 mr-2" />
                {config.label}
              </TabsTrigger>
            );
          })}
        </TabsList>

        {viewConfigs.map((config) => {
          if (config.disabled || !config.component) return null;
          const Component = config.component;
          
          return (
            <TabsContent
              key={config.id}
              value={config.id}
              className="flex-1 m-0"
            >
              {config.id === ViewType.CHAT ? (
                <Component onClose={onClose} />
              ) : (
                <Component sessionId={sessionId} />
              )}
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
    </BrainstormErrorBoundary>
  );
};