/**
 * Git Integration Component with Diff Viewer
 * Provides comprehensive Git functionality for ClaudeCodeSession
 */

import React, { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import {
  GitBranch,
  GitCommit,
  GitPullRequest,
  GitMerge,
  GitFork,
  Plus,
  Minus,
  RotateCcw,
  Check,
  X,
  Clock,
  User,
  Calendar,
  File,
  Folder,
  ChevronRight,
  ChevronDown,
  RefreshCw,
  Archive,
  Eye,
  EyeOff,
  Settings
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useToast } from "@/contexts/ToastContext";

interface GitStatus {
  branch: string;
  behind: number;
  ahead: number;
  staged: GitFile[];
  unstaged: GitFile[];
  untracked: GitFile[];
  conflicts: GitFile[];
}

interface GitFile {
  path: string;
  status: 'modified' | 'added' | 'deleted' | 'renamed' | 'copied' | 'unmerged';
  additions: number;
  deletions: number;
}

interface GitCommit {
  hash: string;
  author: string;
  date: string;
  message: string;
  files: number;
  additions: number;
  deletions: number;
}

interface GitBranch {
  name: string;
  current: boolean;
  remote?: string;
  behind?: number;
  ahead?: number;
}

interface DiffLine {
  type: 'context' | 'addition' | 'deletion';
  oldNumber?: number;
  newNumber?: number;
  content: string;
}

interface DiffHunk {
  oldStart: number;
  oldCount: number;
  newStart: number;
  newCount: number;
  lines: DiffLine[];
}

interface FileDiff {
  path: string;
  oldPath?: string;
  status: GitFile['status'];
  hunks: DiffHunk[];
  binary?: boolean;
}

interface GitIntegrationProps {
  projectPath: string;
  className?: string;
  onFileOpen?: (path: string) => void;
  onPromptSend?: (prompt: string) => void;
}

export const GitIntegration: React.FC<GitIntegrationProps> = ({
  projectPath,
  className,
  onFileOpen,
  onPromptSend
}) => {
  const [status, setStatus] = useState<GitStatus | null>(null);
  const [commits, setCommits] = useState<GitCommit[]>([]);
  const [branches, setBranches] = useState<GitBranch[]>([]);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [fileDiff, setFileDiff] = useState<FileDiff | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [commitMessage, setCommitMessage] = useState("");
  const [showUntracked, setShowUntracked] = useState(true);
  const [selectedTab, setSelectedTab] = useState<"status" | "history" | "branches" | "diff">("status");
  
  const { addToast } = useToast();

  // Mock Git operations - in real implementation, these would call Tauri commands
  const mockGitStatus = useCallback((): Promise<GitStatus> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          branch: "main",
          behind: 0,
          ahead: 2,
          staged: [
            { path: "src/components/GitIntegration.tsx", status: "added", additions: 150, deletions: 0 },
            { path: "src/components/ClaudeCodeSession.simple.tsx", status: "modified", additions: 12, deletions: 3 }
          ],
          unstaged: [
            { path: "src/types/git.ts", status: "modified", additions: 8, deletions: 2 },
            { path: "README.md", status: "modified", additions: 5, deletions: 1 }
          ],
          untracked: [
            { path: "src/lib/git-utils.ts", status: "added", additions: 0, deletions: 0 },
            { path: "docs/git-integration.md", status: "added", additions: 0, deletions: 0 }
          ],
          conflicts: []
        });
      }, 500);
    });
  }, []);

  const mockGitLog = useCallback((): Promise<GitCommit[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            hash: "abc123def",
            author: "Developer",
            date: "2024-01-15T10:30:00Z",
            message: "feat: add comprehensive Git integration with diff viewer",
            files: 5,
            additions: 234,
            deletions: 12
          },
          {
            hash: "def456ghi",
            author: "Developer",
            date: "2024-01-14T16:45:00Z",
            message: "fix: resolve TypeScript compilation issues in session manager",
            files: 3,
            additions: 18,
            deletions: 25
          },
          {
            hash: "ghi789jkl",
            author: "Developer",
            date: "2024-01-14T09:15:00Z",
            message: "refactor: optimize file explorer performance",
            files: 8,
            additions: 67,
            deletions: 43
          }
        ]);
      }, 300);
    });
  }, []);

  const mockGitBranches = useCallback((): Promise<GitBranch[]> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          { name: "main", current: true, remote: "origin/main", ahead: 2, behind: 0 },
          { name: "feature/git-integration", current: false, remote: "origin/feature/git-integration" },
          { name: "fix/typescript-errors", current: false },
          { name: "develop", current: false, remote: "origin/develop", behind: 5 }
        ]);
      }, 200);
    });
  }, []);

  const mockFileDiff = useCallback((path: string): Promise<FileDiff> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        if (path === "src/components/GitIntegration.tsx") {
          resolve({
            path,
            status: "added",
            hunks: [
              {
                oldStart: 0,
                oldCount: 0,
                newStart: 1,
                newCount: 50,
                lines: [
                  { type: "addition", newNumber: 1, content: "/**" },
                  { type: "addition", newNumber: 2, content: " * Git Integration Component with Diff Viewer" },
                  { type: "addition", newNumber: 3, content: " * Provides comprehensive Git functionality for ClaudeCodeSession" },
                  { type: "addition", newNumber: 4, content: " */" },
                  { type: "addition", newNumber: 5, content: "" },
                  { type: "addition", newNumber: 6, content: "import React, { useState, useEffect, useCallback } from \"react\";" }
                ]
              }
            ]
          });
        } else {
          resolve({
            path,
            status: "modified",
            hunks: [
              {
                oldStart: 45,
                oldCount: 8,
                newStart: 45,
                newCount: 12,
                lines: [
                  { type: "context", oldNumber: 45, newNumber: 45, content: "  const [loading, setLoading] = useState(false);" },
                  { type: "context", oldNumber: 46, newNumber: 46, content: "  const [error, setError] = useState<string | null>(null);" },
                  { type: "deletion", oldNumber: 47, content: "  // Old implementation" },
                  { type: "addition", newNumber: 47, content: "  // Enhanced implementation with Git integration" },
                  { type: "addition", newNumber: 48, content: "  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);" },
                  { type: "context", oldNumber: 48, newNumber: 49, content: "" },
                  { type: "context", oldNumber: 49, newNumber: 50, content: "  const { addToast } = useToast();" }
                ]
              }
            ]
          });
        }
      }, 400);
    });
  }, []);

  // Load Git data
  const refreshGitData = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [gitStatus, gitLog, gitBranches] = await Promise.all([
        mockGitStatus(),
        mockGitLog(),
        mockGitBranches()
      ]);
      
      setStatus(gitStatus);
      setCommits(gitLog);
      setBranches(gitBranches);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load Git data");
    } finally {
      setLoading(false);
    }
  }, [mockGitStatus, mockGitLog, mockGitBranches]);

  // Load file diff
  const loadFileDiff = useCallback(async (path: string) => {
    if (selectedFile === path && fileDiff) return;
    
    try {
      setSelectedFile(path);
      const diff = await mockFileDiff(path);
      setFileDiff(diff);
      setSelectedTab("diff");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load file diff");
    }
  }, [selectedFile, fileDiff, mockFileDiff]);

  // Git operations
  const stageFile = useCallback(async (path: string) => {
    try {
      // Mock staging operation
      addToast({
        title: "File staged",
        description: `${path} has been staged for commit`,
        variant: "success"
      });
      await refreshGitData();
    } catch (err) {
      addToast({
        title: "Failed to stage file",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "error"
      });
    }
  }, [addToast, refreshGitData]);

  const unstageFile = useCallback(async (path: string) => {
    try {
      // Mock unstaging operation
      addToast({
        title: "File unstaged",
        description: `${path} has been unstaged`,
        variant: "success"
      });
      await refreshGitData();
    } catch (err) {
      addToast({
        title: "Failed to unstage file",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "error"
      });
    }
  }, [addToast, refreshGitData]);

  const commitChanges = useCallback(async () => {
    if (!commitMessage.trim()) {
      addToast({
        title: "Commit message required",
        description: "Please enter a commit message",
        variant: "error"
      });
      return;
    }

    try {
      // Mock commit operation
      addToast({
        title: "Changes committed",
        description: `Committed with message: "${commitMessage}"`,
        variant: "success"
      });
      setCommitMessage("");
      await refreshGitData();
    } catch (err) {
      addToast({
        title: "Failed to commit",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "error"
      });
    }
  }, [commitMessage, addToast, refreshGitData]);

  const switchBranch = useCallback(async (branchName: string) => {
    try {
      // Mock branch switch
      addToast({
        title: "Branch switched",
        description: `Switched to branch: ${branchName}`,
        variant: "success"
      });
      await refreshGitData();
    } catch (err) {
      addToast({
        title: "Failed to switch branch",
        description: err instanceof Error ? err.message : "Unknown error",
        variant: "error"
      });
    }
  }, [addToast, refreshGitData]);

  // AI Integration helpers
  const generateCommitMessage = useCallback(() => {
    if (!status?.staged.length) return;
    
    const stagedFiles = status.staged.map(f => f.path).join(", ");
    const prompt = `Generate a conventional commit message for these staged changes: ${stagedFiles}. The changes include ${status.staged.reduce((sum, f) => sum + f.additions, 0)} additions and ${status.staged.reduce((sum, f) => sum + f.deletions, 0)} deletions.`;
    onPromptSend?.(prompt);
  }, [status, onPromptSend]);

  const explainDiff = useCallback(() => {
    if (!fileDiff) return;
    
    const prompt = `Explain the changes in this Git diff for file: ${fileDiff.path}. The file has been ${fileDiff.status} with ${fileDiff.hunks.length} change(s).`;
    onPromptSend?.(prompt);
  }, [fileDiff, onPromptSend]);

  useEffect(() => {
    refreshGitData();
  }, [refreshGitData]);

  const renderFileIcon = (status: GitFile['status']) => {
    switch (status) {
      case 'added': return <Plus className="h-3 w-3 text-green-500" />;
      case 'modified': return <Minus className="h-3 w-3 text-yellow-500" />;
      case 'deleted': return <X className="h-3 w-3 text-red-500" />;
      case 'renamed': return <RotateCcw className="h-3 w-3 text-blue-500" />;
      default: return <File className="h-3 w-3" />;
    }
  };

  const renderDiffLine = (line: DiffLine, index: number) => (
    <div
      key={index}
      className={cn(
        "flex items-start gap-2 px-2 py-0.5 font-mono text-xs",
        line.type === 'addition' && "bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-200",
        line.type === 'deletion' && "bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200",
        line.type === 'context' && "bg-background"
      )}
    >
      <span className="text-muted-foreground w-8 text-right">
        {line.oldNumber || ''}
      </span>
      <span className="text-muted-foreground w-8 text-right">
        {line.newNumber || ''}
      </span>
      <span className="w-4">
        {line.type === 'addition' && '+'}
        {line.type === 'deletion' && '-'}
        {line.type === 'context' && ' '}
      </span>
      <span className="flex-1">{line.content}</span>
    </div>
  );

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <GitBranch className="h-4 w-4 text-primary" />
          <span className="font-medium">Git Integration</span>
          {status && (
            <Badge variant="outline" className="text-xs">
              {status.branch}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={generateCommitMessage}
            disabled={!status?.staged.length}
            title="Generate commit message with AI"
          >
            <GitCommit className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={refreshGitData}
            disabled={loading}
          >
            <RefreshCw className={cn("h-3 w-3", loading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {error && (
        <div className="p-3 bg-destructive/10 border-b text-sm text-destructive">
          {error}
        </div>
      )}

      <Tabs value={selectedTab} onValueChange={(value: any) => setSelectedTab(value)} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-4 mx-3 mt-3">
          <TabsTrigger value="status" className="text-xs">Status</TabsTrigger>
          <TabsTrigger value="history" className="text-xs">History</TabsTrigger>
          <TabsTrigger value="branches" className="text-xs">Branches</TabsTrigger>
          <TabsTrigger value="diff" className="text-xs">Diff</TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-hidden">
          {/* Status Tab */}
          <TabsContent value="status" className="h-full mt-3 mx-3">
            <ScrollArea className="h-full">
              <div className="space-y-4">
                {/* Repository Status */}
                {status && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Repository Status</span>
                      <div className="flex gap-1">
                        {status.ahead > 0 && (
                          <Badge variant="outline" className="text-xs">
                            +{status.ahead}
                          </Badge>
                        )}
                        {status.behind > 0 && (
                          <Badge variant="outline" className="text-xs">
                            -{status.behind}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Staged Changes */}
                {status?.staged.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-green-600 dark:text-green-400">
                        Staged Changes ({status.staged.length})
                      </span>
                    </div>
                    <div className="space-y-1">
                      {status.staged.map((file) => (
                        <div
                          key={file.path}
                          className="flex items-center gap-2 p-2 rounded-md bg-muted/30 cursor-pointer hover:bg-muted/50"
                          onClick={() => loadFileDiff(file.path)}
                        >
                          {renderFileIcon(file.status)}
                          <span className="flex-1 text-sm font-mono">{file.path}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              unstageFile(file.path);
                            }}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Unstaged Changes */}
                {status?.unstaged.length > 0 && (
                  <div className="space-y-2">
                    <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                      Unstaged Changes ({status.unstaged.length})
                    </span>
                    <div className="space-y-1">
                      {status.unstaged.map((file) => (
                        <div
                          key={file.path}
                          className="flex items-center gap-2 p-2 rounded-md bg-muted/30 cursor-pointer hover:bg-muted/50"
                          onClick={() => loadFileDiff(file.path)}
                        >
                          {renderFileIcon(file.status)}
                          <span className="flex-1 text-sm font-mono">{file.path}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              stageFile(file.path);
                            }}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Untracked Files */}
                {status?.untracked.length > 0 && showUntracked && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">
                        Untracked Files ({status.untracked.length})
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowUntracked(!showUntracked)}
                      >
                        {showUntracked ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                      </Button>
                    </div>
                    <div className="space-y-1">
                      {status.untracked.map((file) => (
                        <div
                          key={file.path}
                          className="flex items-center gap-2 p-2 rounded-md bg-muted/30 cursor-pointer hover:bg-muted/50"
                        >
                          {renderFileIcon(file.status)}
                          <span className="flex-1 text-sm font-mono">{file.path}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => stageFile(file.path)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Commit Section */}
                {status?.staged.length > 0 && (
                  <div className="space-y-3 border-t pt-4">
                    <Label htmlFor="commit-message">Commit Message</Label>
                    <Textarea
                      id="commit-message"
                      placeholder="Enter commit message..."
                      value={commitMessage}
                      onChange={(e) => setCommitMessage(e.target.value)}
                      className="min-h-[80px]"
                    />
                    <Button onClick={commitChanges} className="w-full">
                      <GitCommit className="h-4 w-4 mr-2" />
                      Commit Changes
                    </Button>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="h-full mt-3 mx-3">
            <ScrollArea className="h-full">
              <div className="space-y-3">
                {commits.map((commit) => (
                  <div
                    key={commit.hash}
                    className="p-3 rounded-md border bg-muted/30 hover:bg-muted/50 cursor-pointer"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <GitCommit className="h-4 w-4 text-muted-foreground" />
                        <span className="font-mono text-sm">{commit.hash}</span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(commit.date).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-sm mb-2">{commit.message}</p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {commit.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <File className="h-3 w-3" />
                        {commit.files} files
                      </div>
                      <div className="flex items-center gap-1">
                        <Plus className="h-3 w-3 text-green-500" />
                        {commit.additions}
                      </div>
                      <div className="flex items-center gap-1">
                        <Minus className="h-3 w-3 text-red-500" />
                        {commit.deletions}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Branches Tab */}
          <TabsContent value="branches" className="h-full mt-3 mx-3">
            <ScrollArea className="h-full">
              <div className="space-y-2">
                {branches.map((branch) => (
                  <div
                    key={branch.name}
                    className={cn(
                      "flex items-center justify-between p-3 rounded-md border cursor-pointer",
                      branch.current 
                        ? "bg-primary/10 border-primary/30" 
                        : "bg-muted/30 hover:bg-muted/50"
                    )}
                    onClick={() => !branch.current && switchBranch(branch.name)}
                  >
                    <div className="flex items-center gap-2">
                      <GitBranch className={cn(
                        "h-4 w-4",
                        branch.current ? "text-primary" : "text-muted-foreground"
                      )} />
                      <span className={cn(
                        "font-mono",
                        branch.current && "font-semibold"
                      )}>
                        {branch.name}
                      </span>
                      {branch.current && (
                        <Badge variant="default" className="text-xs">
                          current
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {branch.remote && (
                        <span className="text-xs text-muted-foreground">
                          {branch.remote}
                        </span>
                      )}
                      {branch.ahead && (
                        <Badge variant="outline" className="text-xs">
                          +{branch.ahead}
                        </Badge>
                      )}
                      {branch.behind && (
                        <Badge variant="outline" className="text-xs">
                          -{branch.behind}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          {/* Diff Tab */}
          <TabsContent value="diff" className="h-full mt-3">
            {fileDiff ? (
              <div className="h-full flex flex-col">
                <div className="flex items-center justify-between p-3 border-b">
                  <div className="flex items-center gap-2">
                    {renderFileIcon(fileDiff.status)}
                    <span className="font-mono text-sm">{fileDiff.path}</span>
                    <Badge variant="outline" className="text-xs">
                      {fileDiff.status}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={explainDiff}
                    title="Explain diff with AI"
                  >
                    <GitCommit className="h-3 w-3" />
                  </Button>
                </div>
                <ScrollArea className="flex-1">
                  <div className="p-3">
                    {fileDiff.binary ? (
                      <div className="text-center text-muted-foreground py-8">
                        Binary file - no diff available
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {fileDiff.hunks.map((hunk, hunkIndex) => (
                          <div key={hunkIndex} className="border rounded-md overflow-hidden">
                            <div className="bg-muted px-3 py-1 text-xs font-mono">
                              @@ -{hunk.oldStart},{hunk.oldCount} +{hunk.newStart},{hunk.newCount} @@
                            </div>
                            <div>
                              {hunk.lines.map((line, lineIndex) => 
                                renderDiffLine(line, lineIndex)
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <GitBranch className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="text-sm">Select a file to view diff</p>
                </div>
              </div>
            )}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default GitIntegration;