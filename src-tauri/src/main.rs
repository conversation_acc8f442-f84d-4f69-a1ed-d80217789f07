// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod checkpoint;
mod claude_binary;
mod commands;
mod process;
mod security;

use checkpoint::state::CheckpointState;
use commands::agents::{
    cleanup_finished_processes, create_agent, delete_agent, execute_agent, export_agent,
    export_agent_to_file, fetch_github_agent_content, fetch_github_agents, get_agent,
    get_agent_run, get_agent_run_with_real_time_metrics, get_claude_binary_path,
    get_live_session_output, get_session_output, get_session_status, import_agent,
    import_agent_from_file, import_agent_from_github, init_database, kill_agent_session,
    list_agent_runs, list_agent_runs_with_metrics, list_agents, list_claude_installations,
    list_running_sessions, load_agent_session_history, set_claude_binary_path, stream_session_output, update_agent, AgentDb,
};
use commands::github::{fetch_github_trending, fetch_github_users};
use commands::marketplace::{
    init_marketplace_database, marketplace_clear_cache, marketplace_fetch_enhanced_github_agents,
    marketplace_get_agent, marketplace_get_categories, marketplace_get_category, marketplace_get_config,
    marketplace_get_featured_agents, marketplace_get_stats, marketplace_get_suggestions,
    marketplace_install_agent, marketplace_search_agents, marketplace_sync_github,
    marketplace_uninstall_agent, marketplace_update_config, MarketplaceDb,
};
use commands::marketplace_seed::marketplace_seed_sample_data;
use commands::claude::{
    cancel_claude_execution, check_auto_checkpoint, check_claude_version, cleanup_old_checkpoints,
    clear_checkpoint_manager, continue_claude_code, create_checkpoint, execute_claude_code,
    find_claude_md_files, fork_from_checkpoint, get_checkpoint_diff, get_checkpoint_settings,
    get_checkpoint_state_stats, get_claude_session_output, get_claude_settings, get_project_sessions,
    get_recently_modified_files, get_session_timeline, get_system_prompt, list_checkpoints,
    list_directory_contents, list_projects, list_running_claude_sessions, load_session_history,
    open_new_session, read_claude_md_file, restore_checkpoint, resume_claude_code,
    save_claude_md_file, save_claude_settings, save_system_prompt, search_files,
    track_checkpoint_message, track_session_messages, update_checkpoint_settings,
    get_hooks_config, update_hooks_config, validate_hook_command,
    ClaudeProcessState,
};
use commands::mcp::{
    mcp_add, mcp_add_from_claude_desktop, mcp_add_json, mcp_get, mcp_get_server_status, mcp_list,
    mcp_read_project_config, mcp_remove, mcp_reset_project_choices, mcp_save_project_config,
    mcp_serve, mcp_test_connection,
};

use commands::usage::{
    get_session_stats, get_usage_by_date_range, get_usage_details, get_usage_stats,
};
use commands::storage::{
    storage_list_tables, storage_read_table, storage_update_row, storage_delete_row,
    storage_insert_row, storage_execute_sql, storage_reset_database,
};
use commands::brainstorm::{brainstorm_chat, cancel_brainstorm};
use commands::orchestra::{analyze_task_for_agent_assignment, process_natural_language_command, analyze_task_requirements, generate_agent_recommendations, start_mcp_server, stop_mcp_server, check_mcp_servers_health, get_running_mcp_servers, get_agent_performance_metrics, calculate_agent_compatibility, get_agent_recommendation_scores, create_collaboration_channel, send_inter_agent_message, get_collaboration_channels, get_channel_messages, start_collaboration_session, get_active_collaboration_sessions, validate_workflow, execute_workflow, set_shared_memory, get_shared_memory, initialize_session_orchestration, link_sessions, get_workflow_execution_status};
use commands::media::{upload_media_attachment, get_media_attachment, delete_media_attachment, get_media_attachments_for_idea};
use commands::websocket::{start_collaboration_server, stop_collaboration_server, get_collaboration_session_users, WebSocketServerState};
use commands::integrations::{save_integration_config, load_integration_config, delete_integration_config, list_configured_integrations, test_integration_connection};
use commands::session_transfer::{transfer_session_data, create_session_link, get_session_links, save_workflow_template, get_transfer_history, fork_session};
use commands::search_providers::{search_duckduckgo, search_duckduckgo_instant, save_search_provider_config, load_search_provider_config, get_search_stats, search_google_custom, search_semantic_scholar};
use commands::voice_processing::{transcribe_audio, transcribe_audio_chunk, analyze_voice_content, save_voice_note, get_voice_notes};
use process::ProcessRegistryState;
use std::sync::Mutex;
use tauri::Manager;

fn main() {
    // Initialize logger
    env_logger::init();


    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Initialize agents database
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            app.manage(AgentDb(Mutex::new(conn)));

            // Initialize marketplace database
            let marketplace_conn = init_marketplace_database(&app.handle()).expect("Failed to initialize marketplace database");
            app.manage(MarketplaceDb(Mutex::new(marketplace_conn)));

            // Initialize checkpoint state
            let checkpoint_state = CheckpointState::new();

            // Set the Claude directory path
            if let Ok(claude_dir) = dirs::home_dir()
                .ok_or_else(|| "Could not find home directory")
                .and_then(|home| {
                    let claude_path = home.join(".claude");
                    claude_path
                        .canonicalize()
                        .map_err(|_| "Could not find ~/.claude directory")
                })
            {
                let state_clone = checkpoint_state.clone();
                tauri::async_runtime::spawn(async move {
                    state_clone.set_claude_dir(claude_dir).await;
                });
            }

            app.manage(checkpoint_state);

            // Initialize process registry
            app.manage(ProcessRegistryState::default());

            // Initialize Claude process state
            app.manage(ClaudeProcessState::default());

            // Initialize WebSocket server state
            app.manage(WebSocketServerState::default());

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Claude & Project Management
            list_projects,
            get_project_sessions,
            get_claude_settings,
            open_new_session,
            get_system_prompt,
            check_claude_version,
            save_system_prompt,
            save_claude_settings,
            find_claude_md_files,
            read_claude_md_file,
            save_claude_md_file,
            load_session_history,
            execute_claude_code,
            continue_claude_code,
            resume_claude_code,
            cancel_claude_execution,
            list_running_claude_sessions,
            get_claude_session_output,
            list_directory_contents,
            search_files,
            get_recently_modified_files,
            get_hooks_config,
            update_hooks_config,
            validate_hook_command,
            
            // Checkpoint Management
            create_checkpoint,
            restore_checkpoint,
            list_checkpoints,
            fork_from_checkpoint,
            get_session_timeline,
            update_checkpoint_settings,
            get_checkpoint_diff,
            track_checkpoint_message,
            track_session_messages,
            check_auto_checkpoint,
            cleanup_old_checkpoints,
            get_checkpoint_settings,
            clear_checkpoint_manager,
            get_checkpoint_state_stats,
            
            // Agent Management
            list_agents,
            create_agent,
            update_agent,
            delete_agent,
            get_agent,
            execute_agent,
            list_agent_runs,
            get_agent_run,
            list_agent_runs_with_metrics,
            get_agent_run_with_real_time_metrics,
            list_running_sessions,
            kill_agent_session,
            get_session_status,
            cleanup_finished_processes,
            get_session_output,
            get_live_session_output,
            stream_session_output,
            load_agent_session_history,
            get_claude_binary_path,
            set_claude_binary_path,
            list_claude_installations,
            export_agent,
            export_agent_to_file,
            import_agent,
            import_agent_from_file,
            fetch_github_agents,
            fetch_github_agent_content,
            import_agent_from_github,
            
            // Usage & Analytics
            get_usage_stats,
            get_usage_by_date_range,
            get_usage_details,
            get_session_stats,
            
            // MCP (Model Context Protocol)
            mcp_add,
            mcp_list,
            mcp_get,
            mcp_remove,
            mcp_add_json,
            mcp_add_from_claude_desktop,
            mcp_serve,
            mcp_test_connection,
            mcp_reset_project_choices,
            mcp_get_server_status,
            mcp_read_project_config,
            mcp_save_project_config,
            
            // Storage Management
            storage_list_tables,
            storage_read_table,
            storage_update_row,
            storage_delete_row,
            storage_insert_row,
            storage_execute_sql,
            storage_reset_database,
            
            // Slash Commands
            commands::slash_commands::slash_commands_list,
            commands::slash_commands::slash_command_get,
            commands::slash_commands::slash_command_save,
            commands::slash_commands::slash_command_delete,
            
            // GitHub Commands
            fetch_github_trending,
            fetch_github_users,
            
            // Marketplace Commands
            marketplace_search_agents,
            marketplace_get_featured_agents,
            marketplace_get_agent,
            marketplace_get_suggestions,
            marketplace_install_agent,
            marketplace_uninstall_agent,
            marketplace_get_categories,
            marketplace_get_category,
            marketplace_get_config,
            marketplace_update_config,
            marketplace_sync_github,
            marketplace_fetch_enhanced_github_agents,
            marketplace_get_stats,
            marketplace_clear_cache,
            marketplace_seed_sample_data,
            
            // Brainstorm Commands
            brainstorm_chat,
            cancel_brainstorm,
            
            // Orchestra Commands
            analyze_task_for_agent_assignment,
            process_natural_language_command,
            analyze_task_requirements,
            generate_agent_recommendations,
            start_mcp_server,
            stop_mcp_server,
            check_mcp_servers_health,
            get_running_mcp_servers,
            get_agent_performance_metrics,
            calculate_agent_compatibility,
            get_agent_recommendation_scores,
            create_collaboration_channel,
            send_inter_agent_message,
            get_collaboration_channels,
            get_channel_messages,
            start_collaboration_session,
            get_active_collaboration_sessions,
            validate_workflow,
            execute_workflow,
            set_shared_memory,
            get_shared_memory,
            initialize_session_orchestration,
            link_sessions,
            get_workflow_execution_status,
            
            // Media Attachment Commands
            upload_media_attachment,
            get_media_attachment,
            delete_media_attachment,
            get_media_attachments_for_idea,
            
            // WebSocket Collaboration Commands
            start_collaboration_server,
            stop_collaboration_server,
            get_collaboration_session_users,
            
            // Integration Commands
            save_integration_config,
            load_integration_config,
            delete_integration_config,
            list_configured_integrations,
            test_integration_connection,
            
            // Session Transfer Commands
            transfer_session_data,
            create_session_link,
            get_session_links,
            save_workflow_template,
            get_transfer_history,
            fork_session,
            
            // Search Provider Commands
            search_duckduckgo,
            search_duckduckgo_instant,
            save_search_provider_config,
            load_search_provider_config,
            get_search_stats,
            search_google_custom,
            search_semantic_scholar,
            
            // Voice Processing Commands
            transcribe_audio,
            transcribe_audio_chunk,
            analyze_voice_content,
            save_voice_note,
            get_voice_notes,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
